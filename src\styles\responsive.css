/* Responsive Design */

@media (max-width: 1400px) {
  .test-text {
    max-width: 1200px;
    font-size: 2rem;
  }
}

@media (max-width: 1200px) {
  .test-text {
    max-width: 1000px;
    font-size: 1.9rem;
  }
}

@media (max-width: 768px) {
  .main-title {
    font-size: 2rem;
  }

  .test-text {
    font-size: 1.6rem;
    padding: 1.5rem;
    max-width: 95%;
    line-height: 1.4;
  }

  .landing-container {
    gap: 2rem;
  }
}

@media (max-width: 480px) {
  .main-title {
    font-size: 1.5rem;
  }

  .test-text {
    font-size: 1.3rem;
    padding: 1rem;
    max-width: 98%;
    line-height: 1.3;
  }

  .mic-button {
    width: 70px;
    height: 70px;
  }

  .test-mic {
    bottom: 2rem;
  }
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
}
