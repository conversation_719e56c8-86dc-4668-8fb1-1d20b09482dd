:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* View Transition API Setup */
@view-transition {
  navigation: auto;
}

/* View Transition Animations */
@keyframes slide-from-right {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-to-left {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(-100%);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fade-out {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(1.1);
  }
}

@keyframes mic-morph {
  from {
    transform: translateY(0) scale(1);
  }
  to {
    transform: translateY(200px) scale(0.9);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 6px 25px rgba(102, 126, 234, 0.5);
  }
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

@keyframes text-reveal {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
    filter: blur(5px);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

#app {
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* View Transition Names */
.landing-container {
  view-transition-name: landing-page;
}

.test-container {
  view-transition-name: test-page;
}

.main-title {
  view-transition-name: main-title;
}

.test-text {
  view-transition-name: test-text;
}

.mic-button {
  view-transition-name: mic-button;
}

/* View Transition Animations */
::view-transition-old(landing-page) {
  animation: fade-out 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

::view-transition-new(test-page) {
  animation: slide-from-right 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

::view-transition-old(main-title) {
  animation: fade-out 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

::view-transition-new(test-text) {
  animation: text-reveal 1.2s cubic-bezier(0.4, 0, 0.2, 1) 0.4s both;
}

::view-transition-old(mic-button) {
  animation: fade-out 0.3s ease-in-out;
}

::view-transition-new(mic-button) {
  animation: fade-in 0.5s ease-out 0.6s both;
}

/* Enhanced root transition for overall page */
::view-transition-old(root) {
  animation: fade-out 0.4s ease-in-out;
}

::view-transition-new(root) {
  animation: fade-in 0.6s ease-out 0.2s both;
}

/* Landing Page Styles */
.landing-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  gap: 3rem;
  padding: 2rem;
}

.main-title {
  font-size: 2.5rem;
  font-weight: 600;
  margin: 0;
  max-width: 600px;
  line-height: 1.2;
}

/* Microphone Button Styles */
.mic-button {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.mic-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
}

.mic-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
  animation: pulse 2s infinite;
}

.mic-button:active::before {
  animation: ripple 0.6s ease-out;
}

.mic-button:active {
  transform: translateY(0) scale(0.95);
  box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
}

.landing-mic {
  animation: pulse 3s infinite;
}

/* Test Page Styles */
.test-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: 2rem;
  gap: 3rem;
}

.test-text {
  font-size: 1.5rem;
  line-height: 1.6;
  max-width: 800px;
  text-align: center;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  animation: text-reveal 1s ease-out 0.5s both;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.test-mic {
  position: fixed;
  bottom: 3rem;
  left: 50%;
  transform: translateX(-50%);
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-title {
    font-size: 2rem;
  }

  .test-text {
    font-size: 1.2rem;
    padding: 1.5rem;
  }

  .landing-container {
    gap: 2rem;
  }
}

@media (max-width: 480px) {
  .main-title {
    font-size: 1.5rem;
  }

  .test-text {
    font-size: 1rem;
    padding: 1rem;
  }

  .mic-button {
    width: 70px;
    height: 70px;
  }
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }

  .test-text {
    background: rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.1);
  }
}
