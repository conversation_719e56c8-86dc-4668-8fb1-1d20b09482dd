/* Animation keyframes and view transitions */

/* View Transition API Setup */
@view-transition {
  navigation: auto;
}

/* Keyframe Animations */
@keyframes slide-from-right {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-to-left {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(-100%);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fade-out {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(1.1);
  }
}

@keyframes mic-move-to-bottom {
  from {
    position: relative;
    bottom: auto;
    left: auto;
    transform: translateX(0) translateY(0);
  }
  to {
    position: fixed;
    bottom: 3rem;
    left: 50%;
    transform: translateX(-50%) translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 6px 25px rgba(102, 126, 234, 0.5);
  }
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

@keyframes text-reveal {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.98);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes title-fade-out {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateY(-30px) scale(0.9);
  }
}

/* View Transition Names */
.landing-container {
  view-transition-name: landing-page;
}

.test-container {
  view-transition-name: test-page;
}

.test-text {
  view-transition-name: test-text;
}

.mic-button {
  view-transition-name: mic-button;
}

/* View Transition Animations */
::view-transition-old(landing-page) {
  animation: title-fade-out 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

::view-transition-new(test-page) {
  animation: fade-in 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.2s both;
}

::view-transition-new(test-text) {
  animation: text-reveal 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 1s both;
}

::view-transition-old(mic-button) {
  animation: fade-out 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

::view-transition-new(mic-button) {
  animation: fade-in 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.2s both;
}

/* Enhanced root transition for overall page - disabled for initial load */
::view-transition-old(root) {
  animation: none;
}

::view-transition-new(root) {
  animation: none;
}
