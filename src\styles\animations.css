/* Animation keyframes and view transitions */

/* View Transition API Setup */
@view-transition {
  navigation: auto;
}

/* Keyframe Animations */
@keyframes slide-from-right {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-to-left {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(-100%);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fade-out {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(1.1);
  }
}

@keyframes mic-morph {
  from {
    transform: translateY(0) scale(1);
  }
  to {
    transform: translateY(200px) scale(0.9);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 6px 25px rgba(102, 126, 234, 0.5);
  }
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

@keyframes text-reveal {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
    filter: blur(5px);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

@keyframes title-fade-out {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
  100% {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
    filter: blur(3px);
  }
}

/* View Transition Names */
.landing-container {
  view-transition-name: landing-page;
}

.test-container {
  view-transition-name: test-page;
}

.test-text {
  view-transition-name: test-text;
}

.mic-button {
  view-transition-name: mic-button;
}

/* View Transition Animations */
::view-transition-old(landing-page) {
  animation: fade-out 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

::view-transition-new(test-page) {
  animation: slide-from-right 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

::view-transition-new(test-text) {
  animation: text-reveal 1.2s cubic-bezier(0.4, 0, 0.2, 1) 0.4s both;
}

::view-transition-old(mic-button) {
  animation: fade-out 0.3s ease-in-out;
}

::view-transition-new(mic-button) {
  animation: fade-in 0.5s ease-out 0.6s both;
}

/* Enhanced root transition for overall page */
::view-transition-old(root) {
  animation: fade-out 0.4s ease-in-out;
}

::view-transition-new(root) {
  animation: fade-in 0.6s ease-out 0.2s both;
}
