/* Component styles */

/* Landing Page Styles */
.landing-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  gap: 3rem;
  padding: 2rem;
}

.main-title {
  font-size: 2.5rem;
  font-weight: 600;
  margin: 0;
  max-width: 600px;
  line-height: 1.2;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.main-title.fade-out {
  animation: title-fade-out 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Microphone Button Styles */
.mic-button {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.mic-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
}

.mic-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
  animation: pulse 2s infinite;
}

.mic-button:active::before {
  animation: ripple 0.6s ease-out;
}

.mic-button:active {
  transform: translateY(0) scale(0.95);
  box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
}

.landing-mic {
  animation: pulse 3s infinite;
}

/* Test Page Styles */
.test-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: 2rem;
  gap: 3rem;
}

.test-text {
  font-size: 2.2rem;
  line-height: 1.5;
  max-width: 1400px;
  width: 95%;
  text-align: center;
  padding: 2rem;

  font-weight: 400;
  letter-spacing: 0.02em;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.test-mic {
  position: fixed;
  bottom: 3rem;
  left: 50%;
  transform: translateX(-50%);
}

.test-mic:hover {
  transform: translateX(-50%) translateY(-2px);
  animation: none; /* Override the pulse animation for bottom mic */
}

.test-mic:active {
  transform: translateX(-50%) translateY(0) scale(0.95);
}
