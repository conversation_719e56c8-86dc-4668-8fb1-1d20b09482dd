import './style.css'

// State management
let currentView = 'landing'; // 'landing' or 'test'

// Sample text for the speaking test
const testText = "I went to the store to buy some groceries. The store was busy, and there was a long line at the checkout. I still managed to get everything I needed before going home.";

// Initialize the app
function initApp() {
  showLandingPage();
}

// Check if View Transition API is supported
function supportsViewTransitions() {
  return 'startViewTransition' in document;
}

// Transition helper function
function transitionHelper(updateCallback) {
  if (supportsViewTransitions()) {
    return document.startViewTransition(updateCallback);
  } else {
    // Fallback for browsers that don't support View Transitions
    const app = document.querySelector('#app');
    app.style.opacity = '0';
    app.style.transform = 'scale(0.95)';

    setTimeout(() => {
      updateCallback();
      app.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
      app.style.opacity = '1';
      app.style.transform = 'scale(1)';

      setTimeout(() => {
        app.style.transition = '';
      }, 500);
    }, 100);
  }
}

// Show landing page
function showLandingPage() {
  const updateDOM = () => {
    currentView = 'landing';
    document.querySelector('#app').innerHTML = `
      <div class="landing-container">
        <h1 class="main-title">Wanna test how good your speaking skill is?</h1>
        <button id="mic-button" class="mic-button landing-mic">
          <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 1C10.34 1 9 2.34 9 4V12C9 13.66 10.34 15 12 15C13.66 15 15 13.66 15 12V4C15 2.34 13.66 1 12 1Z" fill="currentColor"/>
            <path d="M19 10V12C19 16.42 15.42 20 11 20H13C17.42 20 21 16.42 21 12V10H19Z" fill="currentColor"/>
            <path d="M7 12V10H5V12C5 16.42 8.58 20 13 20H11C6.58 20 3 16.42 3 12Z" fill="currentColor"/>
            <path d="M11 22H13V24H11V22Z" fill="currentColor"/>
          </svg>
        </button>
      </div>
    `;

    // Add event listener for mic button
    document.getElementById('mic-button').addEventListener('click', showTestPage);
  };

  transitionHelper(updateDOM);
}

// Show test page
function showTestPage() {
  // Prevent multiple calls
  if (currentView === 'test') return;

  // First, animate the title out smoothly
  const title = document.querySelector('.main-title');
  if (title && !title.classList.contains('fade-out')) {
    title.classList.add('fade-out');

    // Wait for title animation to complete, then transition
    setTimeout(() => {
      const updateDOM = () => {
        currentView = 'test';
        document.querySelector('#app').innerHTML = `
          <div class="test-container">
            <div class="test-text">
              ${testText}
            </div>
            <button id="mic-button" class="mic-button test-mic">
              <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 1C10.34 1 9 2.34 9 4V12C9 13.66 10.34 15 12 15C13.66 15 15 13.66 15 12V4C15 2.34 13.66 1 12 1Z" fill="currentColor"/>
                <path d="M19 10V12C19 16.42 15.42 20 11 20H13C17.42 20 21 16.42 21 12V10H19Z" fill="currentColor"/>
                <path d="M7 12V10H5V12C5 16.42 8.58 20 13 20H11C6.58 20 3 16.42 3 12Z" fill="currentColor"/>
                <path d="M11 22H13V24H11V22Z" fill="currentColor"/>
              </svg>
            </button>
          </div>
        `;

        // Add event listener for mic button (you can add recording functionality here later)
        document.getElementById('mic-button').addEventListener('click', () => {
          console.log('Recording started...');
          // Add recording functionality here
          // You could also add a transition back to landing page here
          // showLandingPage();
        });
      };

      // Use simple DOM update instead of view transition for smoother experience
      updateDOM();
    }, 600); // Wait for title fade-out to complete
  }
}

// Initialize the app when DOM is loaded
initApp();
